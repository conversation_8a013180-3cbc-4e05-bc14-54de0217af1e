import { Routes } from '@angular/router';
import { authGuard } from './guards/auth.guard';

export const routes: Routes = [
  {
    path: 'workers',
    loadComponent: () => import('./pages/worker-list/worker-list.component').then(m => m.WorkerListComponent),
    canActivate: [authGuard]
  },
  {
    path: 'workers/create',
    loadComponent: () => import('./pages/create-worker/create-worker.component').then(m => m.CreateWorkerComponent),
    canActivate: [authGuard]
  },
  {
    path: 'workers/:id',
    loadComponent: () => import('./pages/worker-detail/worker-detail.component').then(m => m.WorkerDetailComponent),
    canActivate: [authGuard]
  },
  {
    path: 'workers/:id/edit',
    loadComponent: () => import('./pages/worker-edit/worker-edit.component').then(m => m.WorkerEditComponent),
    canActivate: [authGuard]
  },
  {
    path: '',
    redirectTo: '/workers',
    pathMatch: 'full'
  }
];
