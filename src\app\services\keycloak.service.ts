import { Injectable } from '@angular/core';
import Keycloak from 'keycloak-js';

const keycloak = new Keycloak({
  url: 'http://localhost:8180/',      // Keycloak URL
  realm: 'jobup-realm',              // Your realm name
  clientId: 'jobup-frontend',        // Your client ID
});

export const initializeKeycloak = () =>
  new Promise((resolve, reject) => {
    keycloak.init({
      onLoad: 'login-required',
      checkLoginIframe: false,
    }).then((authenticated: boolean) => {
      if (authenticated) {
        resolve(true);
      } else {
        reject('Not authenticated');
      }
    }).catch(reject);
  });

export const KeycloakInstance = keycloak;

@Injectable({
  providedIn: 'root'
})
export class KeycloakService {
  constructor() { }

  get username(): string | undefined {
    return KeycloakInstance.tokenParsed?.preferred_username;
  }

  logout(): void {
    KeycloakInstance.logout();
  }

  getToken(): string | undefined {
    return KeycloakInstance.token;
  }
}
