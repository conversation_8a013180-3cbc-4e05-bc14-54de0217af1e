import { Component, inject } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { KeycloakService } from './services/keycloak.service';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [RouterOutlet],
  template: `
    <div>
      <h1>Welcome, {{ keycloakService.username }}</h1>
      <button (click)="keycloakService.logout()">Logout</button>
    </div>
    <router-outlet />
  `,
  styleUrl: './app.component.css'
})
export class AppComponent {
  title = 'jobup-frontend';
  keycloakService = inject(KeycloakService);
}
