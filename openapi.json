{"openapi": "3.0.1", "info": {"title": "OpenAPI definition", "version": "v0"}, "servers": [{"url": "http://localhost:8083/JobUp", "description": "Generated server url"}], "paths": {"/api/workers/{id}": {"get": {"tags": ["worker-controller"], "operationId": "getWorkerById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkerResponseDto"}}}}}}, "put": {"tags": ["worker-controller"], "operationId": "updateWorker", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkerUpdateDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkerResponseDto"}}}}}}, "delete": {"tags": ["worker-controller"], "operationId": "deleteWorker", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/workers": {"get": {"tags": ["worker-controller"], "operationId": "getAllWorkers", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkerResponseDto"}}}}}}}, "post": {"tags": ["worker-controller"], "operationId": "createWorker", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkerCreateDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkerResponseDto"}}}}}}}, "/api/workers/search/location": {"get": {"tags": ["worker-controller"], "operationId": "searchByLocation", "parameters": [{"name": "location", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkerResponseDto"}}}}}}}}, "/api/workers/search/job": {"get": {"tags": ["worker-controller"], "operationId": "searchByJobType", "parameters": [{"name": "jobType", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkerResponseDto"}}}}}}}}}, "components": {"schemas": {"WorkerUpdateDto": {"type": "object", "properties": {"fullName": {"type": "string"}, "jobType": {"type": "string"}, "location": {"type": "string"}, "phoneNumber": {"type": "string"}, "description": {"type": "string"}}, "description": "DTO used to update an existing worker"}, "WorkerResponseDto": {"type": "object", "properties": {"id": {"type": "string"}, "fullName": {"type": "string"}, "jobType": {"type": "string"}, "phoneNumber": {"type": "string"}, "location": {"type": "string"}, "rating": {"type": "number", "format": "double"}, "description": {"type": "string"}}, "description": "DTO returned in responses to the frontend"}, "WorkerCreateDto": {"type": "object", "properties": {"fullName": {"type": "string", "example": "<PERSON>"}, "jobType": {"type": "string", "example": "Electrician"}, "phoneNumber": {"type": "string", "example": "+21612345678"}, "location": {"type": "string", "example": "<PERSON><PERSON>"}, "description": {"type": "string", "example": "etc..."}}, "description": "DTO used to create a new worker"}}}}